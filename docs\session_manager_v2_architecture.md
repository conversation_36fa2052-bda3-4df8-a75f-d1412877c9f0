# Session Manager v2 & Orchestrator v3 Architecture

## Overview

Session Manager v2 and Orchestrator v3 represent a complete architectural redesign of the Voice Agents Platform's session lifecycle management. This new architecture addresses the ownership and responsibility issues identified in the previous implementation by establishing clear boundaries and dependencies between components.

## Key Architectural Principles

### 1. Single Source of Truth
Session Manager v2 owns the complete session lifecycle, from creation to cleanup. No other component manages session state independently.

### 2. Dependency Injection
All components receive their dependencies from Session Manager v2, eliminating ad-hoc component creation and ensuring consistent initialization.

### 3. Clear Cleanup Order
Components are cleaned up in a specific order to prevent resource leaks:
1. Memory Manager (save data, clear caches)
2. State Manager (end session, clear state)
3. Orchestrator (stop listeners, clear session data)
4. Redis (remove session keys)
5. Session removal from active sessions

### 4. Resource Management
Comprehensive tracking and cleanup of all resources with no memory leaks or orphaned processes.

## Component Architecture

```
Session Manager v2 (Owner)
├── Orchestrator v3 (receives session_id, workflow_name, dependencies)
├── Memory Manager (created by State Manager, passed to Orchestrator)
├── State Manager (receives session_id, workflow_name, creates Memory Manager)
└── Redis Client (managed centrally, shared across components)
```

## Session Manager v2

### Responsibilities
- **Session Creation**: Generate unique session IDs and initialize all components
- **Component Initialization**: Create and configure State Manager, Memory Manager, and Orchestrator v3
- **Session Management**: Track active sessions and their metadata
- **Dialog Saving**: Coordinate persistent data saving throughout session lifecycle
- **Comprehensive Cleanup**: Orchestrate cleanup of all managed entities in correct order
- **System Shutdown**: Gracefully shutdown entire system with proper resource cleanup

### Key Methods

#### `create_session(workflow_name: str, user_id: Optional[str] = None) -> str`
Creates a new session with proper initialization of all components.

**Process:**
1. Generate unique session ID
2. Create State Manager (which creates its own Memory Manager)
3. Initialize session metadata in contextual memory
4. Store session information in active sessions registry

#### `initialize_orchestrator(session_id: str) -> OrchestratorV3`
Initialize Orchestrator v3 for a session with proper dependency injection.

**Dependencies Injected:**
- Session ID
- Workflow name
- State Manager instance
- Memory Manager instance
- Redis client instance

#### `cleanup_session(session_id: str, reason: str = "session_complete") -> bool`
Perform comprehensive cleanup of a session and all its managed entities.

**Cleanup Order:**
1. Memory Manager cleanup (save persistent data, clear caches)
2. State Manager cleanup (end session, clear state)
3. Orchestrator cleanup (stop listeners, clear session data)
4. Redis cleanup (remove session keys)
5. Remove from active sessions

#### `shutdown()`
Gracefully shutdown the entire system after cleanup completion.

**Process:**
1. Clean up all active sessions
2. Cancel background tasks
3. Close Redis connections
4. Close orchestrator instances

## Orchestrator v3

### Key Differences from v2

#### 1. Initialization Changes
- **Required Parameters**: Accepts session ID as required parameter during creation
- **Workflow Parameter**: Accepts workflow name as parameter when starting/running
- **Dependency Injection**: Receives all dependencies from Session Manager v2

#### 2. Data Retrieval Integration
- **User Query**: Retrieved from contextual memory via Memory Manager ("clean_text" from preprocessing)
- **Agent Responses**: Retrieved from logger system
- **Confidence Scores**: Retrieved from Redis publish messages
- **Workflow States**: Retrieved from State Manager (layer1 and layer2)
- **OpenAI Integration**: Makes OpenAI aware of states during decision-making
- **Prohibited Actions**: Retrieved directly from State Manager

#### 3. Removed Functionality
- **Retry Tracking**: No longer tracks retry counts (moved to individual agents)
- **Dialog Logging**: Moved completely to Session Manager v2

#### 4. State Transition Changes
- **Agent-to-Agent Transitions**: Uses transition logic to move from agent to agent
- **Pipeline State Function**: Invokes transition pipeline state function in State Manager
- **Evaluation and Transition**: Performs evaluation after each pipeline state completion

### Key Methods

#### `__init__(session_id: str, workflow_name: str, state_manager: StateManager, memory_manager: MemoryManager, redis_client: RedisClient)`
Initialize with dependency injection from Session Manager v2.

#### `start(workflow_name: Optional[str] = None)`
Start the orchestrator with optional workflow name override.

#### `handle_agent_completion(message: str)`
Handle agent completion messages and orchestrate the workflow.

**Process:**
1. Parse and validate agent completion message
2. Gather decision context from all sources
3. Make transition decision using OpenAI with full context
4. Execute transition decision (proceed or redo)

#### `_gather_decision_context(a2a_message: A2AMessage) -> Dict[str, Any]`
Gather all necessary context for decision making.

**Context Sources:**
- User query from contextual memory
- Workflow states from State Manager
- Pipeline states from State Manager
- Prohibited actions from State Manager
- Agent confidence scores from Redis
- Agent response data from message

#### `_make_transition_decision(a2a_message: A2AMessage, context: Dict[str, Any]) -> Dict[str, Any]`
Make transition decision using OpenAI with full context awareness.

## Integration Patterns

### Session Creation Flow
```python
# 1. Create Session Manager v2
session_manager = SessionManagerV2()

# 2. Create session (initializes State Manager and Memory Manager)
session_id = await session_manager.create_session("workflow_name", "user_id")

# 3. Initialize Orchestrator v3 with dependency injection
orchestrator = await session_manager.initialize_orchestrator(session_id)

# 4. Start workflow execution
await orchestrator.start()
```

### Session Cleanup Flow
```python
# 1. Save any remaining data
await session_manager.save_dialog_log(session_id)

# 2. Comprehensive cleanup (handles all components)
await session_manager.cleanup_session(session_id, "reason")

# 3. System shutdown (cleans up all sessions)
await session_manager.shutdown()
```

## Benefits of New Architecture

### 1. Clear Ownership
- Session Manager v2 owns session lifecycle
- No ambiguity about which component is responsible for what
- Eliminates ownership conflicts and bugs

### 2. Proper Resource Management
- Guaranteed cleanup order prevents resource leaks
- Comprehensive tracking of all resources
- No orphaned processes or memory leaks

### 3. Dependency Injection
- Components receive dependencies rather than creating them
- Easier testing with mock dependencies
- Better separation of concerns

### 4. Enhanced Decision Making
- Orchestrator v3 has access to all necessary context
- OpenAI decisions are informed by complete state information
- Better workflow orchestration and error handling

### 5. Maintainability
- Clear architectural boundaries
- Easier to add new features
- Reduced coupling between components

## Migration Guide

### From Current Implementation
1. Replace direct StateManager creation with SessionManagerV2.create_session()
2. Replace direct Orchestrator creation with SessionManagerV2.initialize_orchestrator()
3. Use SessionManagerV2.cleanup_session() instead of manual cleanup
4. Use SessionManagerV2.shutdown() for system shutdown

### Testing
- Use dependency injection for easier mocking
- Test session lifecycle with SessionManagerV2
- Verify cleanup order and resource management
- Test error handling and edge cases

## Configuration

### Environment Variables
- `VOICE_AGENT_REDIS_URL`: Redis connection URL
- `OPENAI_API_KEY`: OpenAI API key for decision making
- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)

### Logging
- Uses `core.logger_config.get_module_logger` for consistent logging
- Structured JSONL logging with session context
- Comprehensive error logging and debugging information

## Performance Considerations

### Memory Usage
- Session Manager v2 tracks active sessions in memory
- Cleanup removes sessions from memory immediately
- No memory leaks due to proper cleanup order

### Redis Usage
- Efficient key scanning for cleanup (uses SCAN instead of KEYS)
- Shared Redis client across components
- Proper connection management and cleanup

### Concurrency
- Async/await throughout for non-blocking operations
- Thread-safe session management
- Proper task cancellation and cleanup
