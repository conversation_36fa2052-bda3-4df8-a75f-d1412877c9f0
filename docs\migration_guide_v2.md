# Migration Guide: Session Manager v2 & Orchestrator v3

## Overview

This guide helps teams migrate from the current session management implementation to the new Session Manager v2 and Orchestrator v3 architecture. The migration addresses ownership issues, improves resource management, and provides clearer architectural boundaries.

## Pre-Migration Checklist

### 1. Backup Current Implementation
```bash
# Create backup of current files
cp core/session_manager.py core/session_manager_backup.py
cp agents/orchestrator_agent_v2.py agents/orchestrator_agent_v2_backup.py
```

### 2. Review Dependencies
- Ensure `core.logger_config` is available
- Verify Redis connectivity
- Check OpenAI API key configuration
- Review workflow configurations

### 3. Test Environment Setup
- Set up isolated test environment
- Configure test Redis instance
- Prepare test workflows and data

## Migration Steps

### Step 1: Update Imports

#### Before (Old Implementation)
```python
from core.session_manager import SessionManager
from agents.orchestrator_agent_v2 import OrchestratorV2
from core.state_mgmt.StateManager import StateManager
```

#### After (New Implementation)
```python
from core.session_manager_v2 import SessionManagerV2
from agents.orchestrator_agent_v3 import OrchestratorV3
# StateManager is managed by SessionManagerV2, no direct import needed
```

### Step 2: Session Creation

#### Before (Old Implementation)
```python
# Manual component creation
redis_client = RedisClient()
state_manager = await StateManager.create(workflow_name, session_id)
orchestrator = OrchestratorV2(redis_url, workflow_name)

# Manual session tracking
sessions = {}
sessions[session_id] = state_manager
```

#### After (New Implementation)
```python
# Centralized session management
session_manager = SessionManagerV2()

# Single call creates all components
session_id = await session_manager.create_session(workflow_name, user_id)

# Orchestrator initialization with dependency injection
orchestrator = await session_manager.initialize_orchestrator(session_id)
```

### Step 3: Workflow Execution

#### Before (Old Implementation)
```python
# Start orchestrator manually
await orchestrator.orch_Start()

# Manual workflow execution
await orchestrator.run_session(workflow_name, audio_file, session_id)
```

#### After (New Implementation)
```python
# Start orchestrator with workflow
await orchestrator.start(workflow_name)

# Workflow execution is handled automatically through agent completions
```

### Step 4: Session Cleanup

#### Before (Old Implementation)
```python
# Manual cleanup in multiple places
await state_manager.end_session_cleanup()
await state_manager.memory_manager.save_persistent_memory_data()
await orchestrator._cleanup_session(session_id, agent_name, reason)
await redis_client.close()

# Manual session removal
del sessions[session_id]
```

#### After (New Implementation)
```python
# Single comprehensive cleanup call
await session_manager.cleanup_session(session_id, reason)

# System shutdown handles all sessions
await session_manager.shutdown()
```

### Step 5: Dialog Logging

#### Before (Old Implementation)
```python
# Dialog logging in orchestrator
await self._save_dialog_log(session_id)

# Manual memory manager calls
await memory_manager.save_persistent_memory_data()
```

#### After (New Implementation)
```python
# Dialog logging through session manager
await session_manager.save_dialog_log(session_id)

# Automatic during cleanup
await session_manager.cleanup_session(session_id, reason)
```

## Code Examples

### Complete Migration Example

#### Before (Old Implementation)
```python
class OldVoicePlatform:
    def __init__(self):
        self.redis_client = RedisClient()
        self.sessions = {}
        self.orchestrators = {}
    
    async def start_session(self, workflow_name, user_id):
        session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        # Manual component creation
        state_manager = await StateManager.create(workflow_name, session_id, user_id)
        orchestrator = OrchestratorV2(workflow_name=workflow_name)
        
        # Manual tracking
        self.sessions[session_id] = state_manager
        self.orchestrators[session_id] = orchestrator
        
        return session_id
    
    async def cleanup_session(self, session_id):
        # Manual cleanup
        if session_id in self.sessions:
            state_manager = self.sessions[session_id]
            await state_manager.end_session_cleanup()
            await state_manager.memory_manager.save_persistent_memory_data()
            del self.sessions[session_id]
        
        if session_id in self.orchestrators:
            orchestrator = self.orchestrators[session_id]
            await orchestrator.close()
            del self.orchestrators[session_id]
```

#### After (New Implementation)
```python
class NewVoicePlatform:
    def __init__(self):
        self.session_manager = SessionManagerV2()
    
    async def start_session(self, workflow_name, user_id):
        # Single call handles all component creation
        session_id = await self.session_manager.create_session(workflow_name, user_id)
        
        # Initialize orchestrator with dependency injection
        orchestrator = await self.session_manager.initialize_orchestrator(session_id)
        
        return session_id
    
    async def cleanup_session(self, session_id):
        # Single call handles comprehensive cleanup
        await self.session_manager.cleanup_session(session_id, "user_request")
    
    async def shutdown(self):
        # Handles all sessions and resources
        await self.session_manager.shutdown()
```

## Testing Migration

### 1. Unit Tests
```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_session_creation_migration():
    """Test that new implementation creates sessions correctly."""
    
    with patch('core.session_manager_v2.StateManager.create') as mock_state_create:
        mock_state_manager = AsyncMock()
        mock_state_manager.memory_manager = AsyncMock()
        mock_state_create.return_value = mock_state_manager
        
        session_manager = SessionManagerV2()
        session_id = await session_manager.create_session("test_workflow", "test_user")
        
        assert session_id is not None
        assert session_id in session_manager.active_sessions
```

### 2. Integration Tests
```python
@pytest.mark.asyncio
async def test_full_session_lifecycle():
    """Test complete session lifecycle with new implementation."""
    
    session_manager = SessionManagerV2()
    
    try:
        # Create session
        session_id = await session_manager.create_session("test_workflow", "test_user")
        
        # Initialize orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)
        
        # Save dialog
        result = await session_manager.save_dialog_log(session_id)
        assert result is True
        
        # Cleanup
        result = await session_manager.cleanup_session(session_id, "test_complete")
        assert result is True
        
    finally:
        await session_manager.shutdown()
```

## Common Migration Issues

### 1. Import Errors
**Issue**: `ImportError: cannot import name 'OrchestratorV3'`

**Solution**: Ensure new files are in correct locations and imports are updated.

### 2. Dependency Injection Errors
**Issue**: Components trying to create their own dependencies

**Solution**: Use SessionManagerV2 for all component creation, don't create components directly.

### 3. Cleanup Order Issues
**Issue**: Resources not being cleaned up properly

**Solution**: Always use `session_manager.cleanup_session()` instead of manual cleanup.

### 4. Logging Errors
**Issue**: Logger method signature mismatches

**Solution**: Use `get_module_logger` from `core.logger_config` for consistent logging.

## Rollback Plan

If migration issues occur, follow this rollback procedure:

### 1. Immediate Rollback
```bash
# Restore backup files
cp core/session_manager_backup.py core/session_manager.py
cp agents/orchestrator_agent_v2_backup.py agents/orchestrator_agent_v2.py

# Restart services
systemctl restart voice-agents-platform
```

### 2. Code Rollback
```python
# Revert to old implementation
from core.session_manager import SessionManager  # Old implementation
from agents.orchestrator_agent_v2 import OrchestratorV2  # Old implementation
```

### 3. Data Recovery
- Check Redis for any orphaned session data
- Verify persistent memory data integrity
- Restore from backups if necessary

## Post-Migration Verification

### 1. Functional Tests
- [ ] Session creation works correctly
- [ ] Orchestrator initialization succeeds
- [ ] Workflow execution completes
- [ ] Dialog logging functions properly
- [ ] Session cleanup removes all resources
- [ ] System shutdown is graceful

### 2. Performance Tests
- [ ] Memory usage is stable
- [ ] No memory leaks detected
- [ ] Redis connections are properly managed
- [ ] Response times are acceptable

### 3. Error Handling Tests
- [ ] Non-existent session handling
- [ ] Component initialization failures
- [ ] Network connectivity issues
- [ ] Resource cleanup on errors

## Support and Troubleshooting

### Common Commands
```bash
# Check active sessions
python -c "from core.session_manager_v2 import SessionManagerV2; sm = SessionManagerV2(); print(sm.list_active_sessions())"

# Test session creation
python examples/session_manager_v2_usage.py

# Run integration tests
python tests/simple_session_test.py
```

### Debugging Tips
1. Enable debug logging: Set verbosity to "debug" in logger configuration
2. Check Redis keys: Use `redis-cli KEYS "session:*"` to see session data
3. Monitor memory usage: Use memory profiling tools during testing
4. Verify component initialization order in logs

### Getting Help
- Review architecture documentation: `docs/session_manager_v2_architecture.md`
- Check example usage: `examples/session_manager_v2_usage.py`
- Run test suite: `tests/simple_session_test.py`
- Contact development team for complex migration issues
